import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import { Analytics } from "@vercel/analytics/next"
import { Suspense } from "react"
import "./globals.css"

const canela = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-canela",
})

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
})

export const metadata: Metadata = {
  title: "HallucinationGuard - Sleep Better. Trust AI.",
  description:
    "Protect teams from AI hallucinations with real-time fact-checking, confidence scores, and audit trails inside Slack.",
  generator: "v0.app",
  icons: {
    icon: "/Auremlogo-min.jpeg",
    shortcut: "/Auremlogo-min.jpeg",
    apple: "/Auremlogo-min.jpeg",
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={`font-sans ${canela.variable} ${inter.variable}`}>
        <Suspense fallback={<div>Loading...</div>}>{children}</Suspense>
        <Analytics />

        <script type="module" dangerouslySetInnerHTML={{
          __html: `
            // Import the functions you need from the SDKs you need
            import { initializeApp } from "https://www.gstatic.com/firebasejs/12.2.1/firebase-app.js";
            import { getAnalytics } from "https://www.gstatic.com/firebasejs/12.2.1/firebase-analytics.js";
            // TODO: Add SDKs for Firebase products that you want to use
            // https://firebase.google.com/docs/web/setup#available-libraries

            // Your web app's Firebase configuration
            // For Firebase JS SDK v7.20.0 and later, measurementId is optional
            const firebaseConfig = {
              apiKey: "AIzaSyDTonv72QvoWOkG_VFIpGkLO9OGHwc35AE",
              authDomain: "aurem-851f6.firebaseapp.com",
              projectId: "aurem-851f6",
              storageBucket: "aurem-851f6.firebasestorage.app",
              messagingSenderId: "919782708836",
              appId: "1:919782708836:web:2ff62e05a686ae049db171",
              measurementId: "G-JBK41NBVSS"
            };

            // Initialize Firebase
            const app = initializeApp(firebaseConfig);
            const analytics = getAnalytics(app);
          `
        }} />
      </body>
    </html>
  )
}
